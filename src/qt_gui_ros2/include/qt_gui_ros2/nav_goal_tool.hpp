#ifndef NAV_GOAL_TOOL_HPP
#define NAV_GOAL_TOOL_HPP

#include <rviz_common/tool.hpp>
#include <rviz_common/display_context.hpp>
#include <rviz_common/viewport_mouse_event.hpp>
#include <rviz_common/load_resource.hpp>
#include <rviz_rendering/rviz_rendering/geometry.hpp>
#include <rviz_rendering/rviz_rendering/objects/arrow.hpp>

#include <QCursor>
#include <QObject>

#include <geometry_msgs/msg/pose_stamped.hpp>
#include <geometry_msgs/msg/point.hpp>

namespace qt_gui_ros2
{

class NavGoalTool : public rviz_common::Tool
{
Q_OBJECT

public:
    NavGoalTool();
    virtual ~NavGoalTool();

    void onInitialize() override;
    void activate() override;
    void deactivate() override;

    int processMouseEvent(rviz_common::ViewportMouseEvent& event) override;

Q_SIGNALS:
    void goalSet(double x, double y, double yaw);

private:
    void makeArrow(const Ogre::Vector3& position, const Ogre::Quaternion& orientation);

    std::shared_ptr<rviz_rendering::Arrow> arrow_;
    enum State
    {
        Position,
        Orientation
    };
    State state_;
    Ogre::Vector3 pos_;
};

} // namespace qt_gui_ros2

#endif // NAV_GOAL_TOOL_HPP
