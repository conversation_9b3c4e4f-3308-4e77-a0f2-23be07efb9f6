#include "qt_gui_ros2/nav_goal_tool.hpp"

#include <rviz_common/display_context.hpp>
#include <rviz_common/viewport_mouse_event.hpp>
#include <rviz_rendering/rviz_rendering/geometry.hpp>
#include <rviz_rendering/rviz_rendering/objects/arrow.hpp>
#include <rviz_rendering/rviz_rendering/render_window.hpp>

#include <OgreSceneNode.h>
#include <OgreSceneManager.h>
#include <OgreEntity.h>

#include <QDebug>

namespace qt_gui_ros2
{

NavGoalTool::NavGoalTool()
    : rviz_common::Tool(), state_(Position)
{
    shortcut_key_ = 'g';
}

NavGoalTool::~NavGoalTool()
{
}

void NavGoalTool::onInitialize()
{
    arrow_ = std::make_shared<rviz_rendering::Arrow>(
        context_->getSceneManager(), nullptr, 2.0f, 0.2f, 0.5f, 0.35f);
    arrow_->setColor(0.0f, 1.0f, 0.0f, 1.0f);
    arrow_->getSceneNode()->setVisible(false);
}

void NavGoalTool::activate()
{
    state_ = Position;
    context_->getViewportMouseEvent().setCursor(Qt::CrossCursor);
}

void NavGoalTool::deactivate()
{
    if (arrow_) {
        arrow_->getSceneNode()->setVisible(false);
    }
}

int NavGoalTool::processMouseEvent(rviz_common::ViewportMouseEvent& event)
{
    if (!arrow_) {
        return Render;
    }

    Ogre::Vector3 intersection;
    Ogre::Plane ground_plane(Ogre::Vector3::UNIT_Z, 0.0f);

    if (rviz_rendering::getPointOnPlaneFromWindowXY(
            event.viewport, ground_plane, event.x, event.y, intersection))
    {
        if (event.leftDown())
        {
            if (state_ == Position)
            {
                pos_ = intersection;
                arrow_->setPosition(pos_);
                arrow_->getSceneNode()->setVisible(true);
                state_ = Orientation;
            }
            else if (state_ == Orientation)
            {
                // 计算朝向
                Ogre::Vector3 cur_pos = intersection;
                Ogre::Vector3 direction = cur_pos - pos_;
                direction.z = 0.0f;
                direction.normalise();

                double yaw = atan2(direction.y, direction.x);

                // 设置箭头朝向
                Ogre::Quaternion orient = Ogre::Quaternion(Ogre::Radian(yaw), Ogre::Vector3::UNIT_Z);
                arrow_->setOrientation(orient);

                // 发送目标信号
                Q_EMIT goalSet(pos_.x, pos_.y, yaw);

                qDebug() << "Navigation goal set: (" << pos_.x << ", " << pos_.y << ", " << yaw << ")";

                // 重置状态
                state_ = Position;
                arrow_->getSceneNode()->setVisible(false);
            }
        }
        else if (state_ == Orientation)
        {
            // 更新箭头朝向预览
            Ogre::Vector3 cur_pos = intersection;
            Ogre::Vector3 direction = cur_pos - pos_;
            direction.z = 0.0f;
            direction.normalise();

            double yaw = atan2(direction.y, direction.x);
            Ogre::Quaternion orient = Ogre::Quaternion(Ogre::Radian(yaw), Ogre::Vector3::UNIT_Z);
            arrow_->setOrientation(orient);
        }
    }

    return Render;
}

void NavGoalTool::makeArrow(const Ogre::Vector3& position, const Ogre::Quaternion& orientation)
{
    arrow_->setPosition(position);
    arrow_->setOrientation(orientation);
    arrow_->getSceneNode()->setVisible(true);
}

} // namespace qt_gui_ros2

#include <pluginlib/class_list_macros.hpp>
PLUGINLIB_EXPORT_CLASS(qt_gui_ros2::NavGoalTool, rviz_common::Tool)
