#include "qt_gui_ros2/mainWindow.hpp"

#include <QGridLayout>
#include <geometry_msgs/msg/point.hpp>
#include <rviz_common/view_manager.hpp>
#include <rviz_common/display_context.hpp>
#include <rviz_rendering/render_window.hpp>
#include <QVector3D>
#include <QDebug>
#include <rviz_common/tool_manager.hpp>
#include <rviz_common/view_manager.hpp>

MainWindow::MainWindow(QApplication *app, rviz_common::ros_integration::RosNodeAbstractionIface::WeakPtr rviz_ros_node, QWidget *parent)
    : QMainWindow(parent), app_(app), rviz_ros_node_(rviz_ros_node), mapReceived_(false), navigation_active_(false) {

    // 设置窗口标题和大小
    setWindowTitle("RobotCar Navigation Control");
    resize(1200, 800);

    // 设置状态栏
    statusBar()->showMessage("初始化中...");

    qDebug() << "Qt GUI初始化开始";

    try {
        mainLayout_ = new QVBoxLayout;
        centralWidget_ = new QWidget();

    initializeRViz();
    setupJoystickControls();

    // Add frame input box and button
    QLabel *frameLabel = new QLabel("Reference Frame:");
    frameLineEdit_ = new QLineEdit("map");
    QPushButton *updateFrameButton = new QPushButton("Update Frame");
    connect(updateFrameButton, &QPushButton::clicked, this, &MainWindow::updateFrame);

    mainLayout_->addWidget(frameLabel);
    mainLayout_->addWidget(frameLineEdit_);
    mainLayout_->addWidget(updateFrameButton);
    mainLayout_->addWidget(renderPanel_);  // Add the render panel here

        // Initialize cmd_vel publisher for robotcar system
        // 使用robotcar系统的差分驱动控制器话题
        cmdVelPublisher_ = rviz_ros_node_.lock()->get_raw_node()->create_publisher<geometry_msgs::msg::Twist>("/diff_drive_controller/cmd_vel_unstamped", 10);
        qDebug() << "cmd_vel发布器初始化成功: /diff_drive_controller/cmd_vel_unstamped";

    // Set up the indicator for map reception
    mapReceivedIndicator_ = new QLabel("Map Received: No", this);
    mapReceivedIndicator_->setStyleSheet("color: red;");
    mainLayout_->addWidget(mapReceivedIndicator_);

    centralWidget_->setLayout(mainLayout_);
    setCentralWidget(centralWidget_);
    
    
    QString frame_id = frameLineEdit_->text();
    // Update the fixed frame in VisualizationManager
    manager_->getRootDisplayGroup()->setFixedFrame(frame_id); // Set for root display group
    manager_->setFixedFrame(frame_id); // Set for frame manager

    // Call updateFixedFrame() to apply changes across the visualization manager
    //manager_->updateFixedFrame();

        setupGridDisplay();
        setupTFDisplay();
        setupMapDisplay();
        setupRobotModelDisplay();
        setupLaserScanDisplay();

        // {{ AURA-X: Add - 设置导航控制界面. }}
        setupNavigationControls();

        // {{ AURA-X: Add - 设置Nav2 action client. }}
        setupNavigationActionClient();

        setupMapSubscriber();

        // 更新状态栏
        statusBar()->showMessage("初始化完成，等待数据...");
        qDebug() << "Qt GUI初始化完成";
    } catch (const std::exception& e) {
        qDebug() << "初始化过程中发生错误: " << e.what();
        statusBar()->showMessage("初始化失败: " + QString(e.what()));
        QMessageBox::critical(this, "初始化错误",
                             "Qt GUI初始化过程中发生错误:\n" + QString(e.what()) +
                             "\n\n请检查ROS系统是否正常运行，并重新启动应用程序。");
    }
}

MainWindow::~MainWindow() {
    // 不在析构函数中调用rclcpp::shutdown()，避免重复关闭
    // shutdown将在main函数中统一处理
    qDebug() << "MainWindow析构函数调用";
}

QWidget *MainWindow::getParentWindow() {
    return this;
}

rviz_common::PanelDockWidget *MainWindow::addPane(const QString &name, QWidget *pane, Qt::DockWidgetArea area, bool floating) {
    return nullptr;
}

void MainWindow::setStatus(const QString &message) {
    // Optional: handle setting a status message here
}

void MainWindow::initializeRViz() {
    app_->processEvents();
    renderPanel_ = new rviz_common::RenderPanel(centralWidget_);
    app_->processEvents();
    renderPanel_->getRenderWindow()->initialize();

    auto clock = rviz_ros_node_.lock()->get_raw_node()->get_clock();
    manager_ = new rviz_common::VisualizationManager(renderPanel_, rviz_ros_node_, this, clock);
    renderPanel_->initialize(manager_);

    // Enable mouse tracking and focus policy to ensure it receives events
    renderPanel_->setMouseTracking(true);
    renderPanel_->setFocusPolicy(Qt::StrongFocus);

    app_->processEvents();
    manager_->initialize();
    manager_->startUpdate();

    // Set the view controller to Orbit to allow for mouse interactions
    manager_->getViewManager()->setCurrentViewControllerType("rviz_default_plugins/Orbit");

    // Retrieve the active view controller to set properties and confirm it's set up correctly
    auto orbit_view_controller = manager_->getViewManager()->getCurrent();
    if (!orbit_view_controller) {
        qDebug() << "Orbit view controller could not be set.";
        return;
    }

    qDebug() << "Orbit view controller initialized successfully.";

    // Set default distance and focal point for the camera
    orbit_view_controller->subProp("Distance")->setValue(10.0);
    orbit_view_controller->subProp("Focal Point")->setValue(QVariant::fromValue(QVector3D(0.0, 0.0, 0.0)));

    // Set initial orientation of the camera
    orbit_view_controller->subProp("Pitch")->setValue(1.5708);  // Example angle in radians
    orbit_view_controller->subProp("Yaw")->setValue(3.14);     // Example angle in radians

    // Set Interact tool as the active tool to enable mouse interactions
    auto tool_manager = manager_->getToolManager();
    tool_manager->setCurrentTool(tool_manager->addTool("rviz_default_plugins/Interact"));
}

void MainWindow::setupGridDisplay() {
    QString frame_id = frameLineEdit_->text();

    // Initialize the grid display
    grid_ = manager_->createDisplay("rviz_default_plugins/Grid", "Grid", true);
    if (grid_) {
        grid_->subProp("Line Style")->setValue("Lines");
        grid_->subProp("Color")->setValue(QColor(Qt::white));
        grid_->subProp("Reference Frame")->setValue(frame_id);
        qDebug() << "Grid display configured for fixed frame:" << frame_id;
    } else {
        qDebug() << "Failed to create Grid display.";
    }
}

void MainWindow::setupTFDisplay() {
    // Set up the TF display to show frames with a fixed frame
    tf_display_ = manager_->createDisplay("rviz_default_plugins/TF", "TF Display", true);
    if (tf_display_) {
        tf_display_->subProp("Show Axes")->setValue(true);
        qDebug() << "TF display configured with axes and names shown.";
    } else {
        qDebug() << "Failed to create TF display.";
    }
}

void MainWindow::setupMapDisplay() {
    QString frame_id = frameLineEdit_->text();

    // Set up the Map display for the /map topic
    map_display_ = manager_->createDisplay("rviz_default_plugins/Map", "Map Display", true);
    if (map_display_) {
        map_display_->subProp("Topic")->setValue("/map");
        map_display_->subProp("Alpha")->setValue(1.0);
        map_display_->subProp("Draw Behind")->setValue(false);
        map_display_->subProp("Color Scheme")->setValue("map");
        map_display_->subProp("Topic")->subProp("Durability Policy")->setValue("Transient Local");
        
        
        
        //map_display_->setEnabled(true);

        qDebug() << "Map display configured for /map topic with fixed frame:" << frame_id;
    } else {
        qDebug() << "Failed to create Map display.";
    }
}



void MainWindow::setupRobotModelDisplay() {
    // Set up the RobotModel display for the /robot_description topic
    robot_model_display_ = manager_->createDisplay("rviz_default_plugins/RobotModel", "RobotModel Display", true);
    if (robot_model_display_) {
        // 尝试使用多个可能的机器人描述话题
        try {
            // 首先检查是否有/robot_description话题
            auto node = rviz_ros_node_.lock()->get_raw_node();
            auto topics = node->get_topic_names_and_types();

            QString robot_description_topic = "/robot_description"; // 默认话题

            // 查找可用的robot_description话题
            for (const auto& topic : topics) {
                if (topic.first.find("robot_description") != std::string::npos) {
                    robot_description_topic = QString::fromStdString(topic.first);
                    qDebug() << "找到机器人描述话题:" << robot_description_topic;
                    break;
                }
            }

            // 设置找到的话题
            robot_model_display_->subProp("Description Topic")->setValue(robot_description_topic);
            qDebug() << "使用机器人描述话题:" << robot_description_topic;
        } catch (const std::exception& e) {
            qDebug() << "设置机器人模型时出错:" << e.what();
            // 回退到默认话题
            robot_model_display_->subProp("Description Topic")->setValue("/robot_description");
        }

        robot_model_display_->subProp("TF Prefix")->setValue("");  // 设置TF前缀为空
        qDebug() << "RobotModel display configured for /robot_description topic.";
    } else {
        qDebug() << "Failed to create RobotModel display.";
    }
}

void MainWindow::setupJoystickControls() {
    QGridLayout *joystickLayout = new QGridLayout;

    forwardButton_ = new QPushButton("Forward");
    backwardButton_ = new QPushButton("Backward");
    leftButton_ = new QPushButton("Left");
    rightButton_ = new QPushButton("Right");
    stopButton_ = new QPushButton("Stop");

    joystickLayout->addWidget(forwardButton_, 0, 1);
    joystickLayout->addWidget(backwardButton_, 2, 1);
    joystickLayout->addWidget(leftButton_, 1, 0);
    joystickLayout->addWidget(rightButton_, 1, 2);
    joystickLayout->addWidget(stopButton_, 1, 1);

    mainLayout_->addLayout(joystickLayout);

    // Connect buttons to send appropriate cmd_vel messages
    connect(forwardButton_, &QPushButton::pressed, this, [this]() {
        currentTwist_.linear.x = 1.0; currentTwist_.angular.z = 0.0;
        sendJoystickCommand();
    });
    connect(backwardButton_, &QPushButton::pressed, this, [this]() {
        currentTwist_.linear.x = -1.0; currentTwist_.angular.z = 0.0;
        sendJoystickCommand();
    });
    connect(leftButton_, &QPushButton::pressed, this, [this]() {
        currentTwist_.linear.x = 0.0; currentTwist_.angular.z = 1.0;
        sendJoystickCommand();
    });
    connect(rightButton_, &QPushButton::pressed, this, [this]() {
        currentTwist_.linear.x = 0.0; currentTwist_.angular.z = -1.0;
        sendJoystickCommand();
    });
    connect(stopButton_, &QPushButton::pressed, this, [this]() {
        currentTwist_.linear.x = 0.0; currentTwist_.angular.z = 0.0;
        sendJoystickCommand();
    });
}

void MainWindow::sendJoystickCommand() {
    cmdVelPublisher_->publish(currentTwist_);
}

void MainWindow::updateFrame() {
    QString frame_id = frameLineEdit_->text();

    // Update the grid display's reference frame
    if (grid_) {
        grid_->subProp("Reference Frame")->setValue(frame_id);
    }

    // Set the fixed frame in the FrameManager directly
    if (manager_ && manager_->getFrameManager()) {
        manager_->setFixedFrame(frame_id); // Set for frame manager
        manager_->getRootDisplayGroup()->setFixedFrame(frame_id); // Set for root display group
        qDebug() << "FrameManager fixed frame updated to:" << frame_id;
    }
}

void MainWindow::closeEvent(QCloseEvent *event) {
    // 不在closeEvent中调用rclcpp::shutdown()，避免重复关闭
    // shutdown将在main函数中统一处理
    qDebug() << "应用程序窗口关闭";
    event->accept();
}

void MainWindow::setupMapSubscriber() {
    auto node = rviz_ros_node_.lock()->get_raw_node();
    mapSubscriber_ = node->create_subscription<nav_msgs::msg::OccupancyGrid>(
        "/map", 10,
        [this](const nav_msgs::msg::OccupancyGrid::SharedPtr msg) {
            Q_UNUSED(msg);
            mapReceived_ = true;
            qDebug() << "Map Received";
            updateMapReceivedIndicator(true);

            // Enable map display if map data is received
            /*if (map_display_) {
                map_display_->setEnabled(true);
            }*/
        }
    );

    // Set a timer to reset the indicator if no map data is received for a while
    auto timer = new QTimer(this);
    connect(timer, &QTimer::timeout, this, [this]() {
        // 检查是否有地图数据
        bool hasMapData = false;
        try {
            // 尝试获取最新的地图消息
            auto node = rviz_ros_node_.lock()->get_raw_node();
            auto map_info = node->get_topic_names_and_types();
            for (const auto& topic : map_info) {
                if (topic.first == "/map") {
                    hasMapData = true;
                    break;
                }
            }

            // 更新地图接收状态
            mapReceived_ = hasMapData;
            updateMapReceivedIndicator(hasMapData);

            // 如果有地图数据但显示未启用，则启用地图显示
            if (hasMapData && map_display_ && !map_display_->isEnabled()) {
                map_display_->setEnabled(true);
                qDebug() << "Map display enabled";
            }
        } catch (const std::exception& e) {
            qDebug() << "Error checking map status:" << e.what();
        }
    });
    timer->start(2000);  // Check every 2 seconds
}

void MainWindow::updateMapReceivedIndicator(bool received) {
    if (received) {
        mapReceivedIndicator_->setText("Map Received: Yes");
        mapReceivedIndicator_->setStyleSheet("color: green;");
    } else {
        mapReceivedIndicator_->setText("Map Received: No");
        mapReceivedIndicator_->setStyleSheet("color: red;");
    }
}

// Set up LaserScan Display
void MainWindow::setupLaserScanDisplay() {
    auto laser_scan_display = manager_->createDisplay("rviz_default_plugins/LaserScan", "LaserScan Display", true);
    if (laser_scan_display) {
        laser_scan_display->subProp("Topic")->setValue("/scan");       // Set to the topic where laser data is published
        laser_scan_display->subProp("Size (m)")->setValue(0.1);        // Adjust point size as needed
        laser_scan_display->subProp("Color")->setValue(QColor(Qt::green));  // Set color of laser points
        qDebug() << "LaserScan display configured successfully for /scan.";
    } else {
        qDebug() << "Failed to configure LaserScan display.";
    }
}

// {{ AURA-X: Add - 导航控制界面设置函数. }}
void MainWindow::setupNavigationControls() {
    // 创建导航控制组
    navigationGroup_ = new QGroupBox("Navigation Control", this);
    QVBoxLayout *navLayout = new QVBoxLayout(navigationGroup_);

    // 目标坐标输入区域
    QGroupBox *goalGroup = new QGroupBox("Goal Position", this);
    QHBoxLayout *goalLayout = new QHBoxLayout(goalGroup);

    // X坐标输入
    goalLayout->addWidget(new QLabel("X:"));
    goalXSpinBox_ = new QDoubleSpinBox();
    goalXSpinBox_->setRange(-100.0, 100.0);
    goalXSpinBox_->setDecimals(2);
    goalXSpinBox_->setSingleStep(0.1);
    goalXSpinBox_->setValue(0.0);
    goalLayout->addWidget(goalXSpinBox_);

    // Y坐标输入
    goalLayout->addWidget(new QLabel("Y:"));
    goalYSpinBox_ = new QDoubleSpinBox();
    goalYSpinBox_->setRange(-100.0, 100.0);
    goalYSpinBox_->setDecimals(2);
    goalYSpinBox_->setSingleStep(0.1);
    goalYSpinBox_->setValue(0.0);
    goalLayout->addWidget(goalYSpinBox_);

    // 朝向输入
    goalLayout->addWidget(new QLabel("Yaw:"));
    goalYawSpinBox_ = new QDoubleSpinBox();
    goalYawSpinBox_->setRange(-3.14159, 3.14159);
    goalYawSpinBox_->setDecimals(3);
    goalYawSpinBox_->setSingleStep(0.1);
    goalYawSpinBox_->setValue(0.0);
    goalLayout->addWidget(goalYawSpinBox_);

    navLayout->addWidget(goalGroup);

    // 控制按钮区域
    QHBoxLayout *buttonLayout = new QHBoxLayout();

    setGoalButton_ = new QPushButton("Set Goal");
    setGoalButton_->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }");
    connect(setGoalButton_, &QPushButton::clicked, this, &MainWindow::onSetNavigationGoal);
    buttonLayout->addWidget(setGoalButton_);

    cancelNavButton_ = new QPushButton("Cancel Navigation");
    cancelNavButton_->setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; }");
    cancelNavButton_->setEnabled(false);
    connect(cancelNavButton_, &QPushButton::clicked, this, &MainWindow::onCancelNavigation);
    buttonLayout->addWidget(cancelNavButton_);

    emergencyStopButton_ = new QPushButton("Emergency Stop");
    emergencyStopButton_->setStyleSheet("QPushButton { background-color: #F44336; color: white; font-weight: bold; }");
    connect(emergencyStopButton_, &QPushButton::clicked, this, &MainWindow::onEmergencyStop);
    buttonLayout->addWidget(emergencyStopButton_);

    navLayout->addLayout(buttonLayout);

    // 导航状态显示
    navigationStatusLabel_ = new QLabel("Navigation Status: Idle");
    navigationStatusLabel_->setStyleSheet("QLabel { font-weight: bold; }");
    navLayout->addWidget(navigationStatusLabel_);

    // 添加导航说明
    QLabel *toolHelpLabel = new QLabel("提示：在上方输入坐标后点击'Set Goal'按钮设置导航目标", this);
    toolHelpLabel->setStyleSheet("QLabel { color: blue; font-style: italic; }");
    navLayout->addWidget(toolHelpLabel);

    // 将导航控制组添加到主布局
    mainLayout_->addWidget(navigationGroup_);

    qDebug() << "导航控制界面设置完成";
}

// {{ AURA-X: Add - Nav2 action client设置函数. }}
void MainWindow::setupNavigationActionClient() {
    try {
        auto rviz_node = rviz_ros_node_.lock();
        if (!rviz_node) {
            qDebug() << "RViz节点未就绪，延迟初始化action client";
            navigationStatusLabel_->setText("Navigation Status: Node Not Ready");
            navigationStatusLabel_->setStyleSheet("QLabel { color: orange; font-weight: bold; }");

            // 使用定时器延迟初始化
            QTimer::singleShot(2000, this, &MainWindow::setupNavigationActionClient);
            return;
        }

        auto node = rviz_node->get_raw_node();
        if (!node) {
            qDebug() << "无法获取原始ROS节点";
            navigationStatusLabel_->setText("Navigation Status: Raw Node Failed");
            navigationStatusLabel_->setStyleSheet("QLabel { color: red; font-weight: bold; }");
            return;
        }

        nav_action_client_ = rclcpp_action::create_client<NavigateToPose>(
            node, "/navigate_to_pose");

        if (nav_action_client_) {
            qDebug() << "Nav2 action client初始化成功: /navigate_to_pose";
            navigationStatusLabel_->setText("Navigation Status: Ready");
            navigationStatusLabel_->setStyleSheet("QLabel { color: green; font-weight: bold; }");
        } else {
            qDebug() << "Nav2 action client创建失败";
            navigationStatusLabel_->setText("Navigation Status: Client Creation Failed");
            navigationStatusLabel_->setStyleSheet("QLabel { color: red; font-weight: bold; }");
        }
    } catch (const std::exception& e) {
        qDebug() << "Nav2 action client初始化失败: " << e.what();
        navigationStatusLabel_->setText("Navigation Status: Exception");
        navigationStatusLabel_->setStyleSheet("QLabel { color: red; font-weight: bold; }");
    }
}

// {{ AURA-X: Add - 导航控制槽函数实现. }}
void MainWindow::onSetNavigationGoal() {
    double x = goalXSpinBox_->value();
    double y = goalYSpinBox_->value();
    double yaw = goalYawSpinBox_->value();

    sendNavigationGoal(x, y, yaw);
}

void MainWindow::onCancelNavigation() {
    if (navigation_active_ && nav_action_client_) {
        nav_action_client_->async_cancel_all_goals();
        navigation_active_ = false;

        // 更新UI状态
        setGoalButton_->setEnabled(true);
        cancelNavButton_->setEnabled(false);
        navigationStatusLabel_->setText("Navigation Status: Cancelled");
        navigationStatusLabel_->setStyleSheet("QLabel { color: orange; font-weight: bold; }");

        qDebug() << "导航已取消";
    }
}

void MainWindow::onEmergencyStop() {
    // 发送停止命令
    geometry_msgs::msg::Twist stop_msg;
    stop_msg.linear.x = 0.0;
    stop_msg.linear.y = 0.0;
    stop_msg.angular.z = 0.0;
    cmdVelPublisher_->publish(stop_msg);

    // 取消导航
    if (navigation_active_) {
        onCancelNavigation();
    }

    navigationStatusLabel_->setText("Navigation Status: Emergency Stop");
    navigationStatusLabel_->setStyleSheet("QLabel { color: red; font-weight: bold; }");

    qDebug() << "紧急停止已执行";
}

// {{ AURA-X: Add - Nav2目标发送核心函数. }}
void MainWindow::sendNavigationGoal(double x, double y, double yaw) {
    if (!nav_action_client_) {
        qDebug() << "Nav2 action client未初始化";
        navigationStatusLabel_->setText("Navigation Status: Client Not Ready");
        navigationStatusLabel_->setStyleSheet("QLabel { color: red; font-weight: bold; }");
        return;
    }

    // 等待action server可用
    if (!nav_action_client_->wait_for_action_server(std::chrono::seconds(5))) {
        qDebug() << "Nav2 action server不可用";
        navigationStatusLabel_->setText("Navigation Status: Server Unavailable");
        navigationStatusLabel_->setStyleSheet("QLabel { color: red; font-weight: bold; }");
        return;
    }

    // 创建导航目标
    auto goal_msg = NavigateToPose::Goal();
    goal_msg.pose.header.frame_id = "map";
    goal_msg.pose.header.stamp = rviz_ros_node_.lock()->get_raw_node()->get_clock()->now();
    goal_msg.pose.pose.position.x = x;
    goal_msg.pose.pose.position.y = y;
    goal_msg.pose.pose.position.z = 0.0;

    // 转换yaw角度为四元数
    goal_msg.pose.pose.orientation.z = sin(yaw / 2.0);
    goal_msg.pose.pose.orientation.w = cos(yaw / 2.0);
    goal_msg.pose.pose.orientation.x = 0.0;
    goal_msg.pose.pose.orientation.y = 0.0;

    // 设置action回调
    auto send_goal_options = rclcpp_action::Client<NavigateToPose>::SendGoalOptions();

    send_goal_options.goal_response_callback =
        [this](const rclcpp_action::ClientGoalHandle<NavigateToPose>::SharedPtr& goal_handle) {
            if (!goal_handle) {
                qDebug() << "导航目标被拒绝";
                navigationStatusLabel_->setText("Navigation Status: Goal Rejected");
                navigationStatusLabel_->setStyleSheet("QLabel { color: red; font-weight: bold; }");
                navigation_active_ = false;
                setGoalButton_->setEnabled(true);
                cancelNavButton_->setEnabled(false);
            } else {
                qDebug() << "导航目标已接受";
                navigationStatusLabel_->setText("Navigation Status: Goal Accepted");
                navigationStatusLabel_->setStyleSheet("QLabel { color: blue; font-weight: bold; }");
            }
        };

    send_goal_options.result_callback =
        [this](const rclcpp_action::ClientGoalHandle<NavigateToPose>::WrappedResult& result) {
            navigation_active_ = false;
            setGoalButton_->setEnabled(true);
            cancelNavButton_->setEnabled(false);

            switch (result.code) {
                case rclcpp_action::ResultCode::SUCCEEDED:
                    qDebug() << "导航成功完成";
                    navigationStatusLabel_->setText("Navigation Status: Succeeded");
                    navigationStatusLabel_->setStyleSheet("QLabel { color: green; font-weight: bold; }");
                    break;
                case rclcpp_action::ResultCode::ABORTED:
                    qDebug() << "导航被中止";
                    navigationStatusLabel_->setText("Navigation Status: Aborted");
                    navigationStatusLabel_->setStyleSheet("QLabel { color: red; font-weight: bold; }");
                    break;
                case rclcpp_action::ResultCode::CANCELED:
                    qDebug() << "导航被取消";
                    navigationStatusLabel_->setText("Navigation Status: Canceled");
                    navigationStatusLabel_->setStyleSheet("QLabel { color: orange; font-weight: bold; }");
                    break;
                default:
                    qDebug() << "导航结果未知";
                    navigationStatusLabel_->setText("Navigation Status: Unknown");
                    navigationStatusLabel_->setStyleSheet("QLabel { color: gray; font-weight: bold; }");
                    break;
            }
        };

    // 发送目标
    nav_action_client_->async_send_goal(goal_msg, send_goal_options);

    // 更新UI状态
    navigation_active_ = true;
    setGoalButton_->setEnabled(false);
    cancelNavButton_->setEnabled(true);
    navigationStatusLabel_->setText("Navigation Status: Navigating...");
    navigationStatusLabel_->setStyleSheet("QLabel { color: blue; font-weight: bold; }");

    qDebug() << QString("发送导航目标: (%.2f, %.2f, %.2f)").arg(x).arg(y).arg(yaw);
}


