#!/usr/bin/env python3
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, LogInfo
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node


def generate_launch_description():
    """
    生成robotcar Qt GUI的launch描述
    """
    
    # 声明launch参数
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='false',
        description='是否使用仿真时间'
    )
    
    frame_id_arg = DeclareLaunchArgument(
        'frame_id',
        default_value='map',
        description='RViz显示的参考坐标系'
    )
    
    robot_base_frame_arg = DeclareLaunchArgument(
        'robot_base_frame',
        default_value='base_footprint',
        description='机器人本体坐标系'
    )
    
    # Qt GUI节点
    qt_gui_node = Node(
        package='qt_gui_ros2',
        executable='qt_gui_ros2',
        name='robotcar_qt_gui',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'frame_id': LaunchConfiguration('frame_id'),
            'robot_base_frame': LaunchConfiguration('robot_base_frame'),
        }],
        # 重映射话题以适配fusion_05_navigation系统
        remappings=[
            # 控制话题重映射
            ('/cmd_vel', '/diff_drive_controller/cmd_vel_unstamped'),
            # 里程计话题重映射 (使用EKF融合后的高质量数据)
            ('/odom', '/odometry/filtered'),
            # 激光雷达话题 (保持默认)
            ('/scan', '/scan'),
            # 地图话题 (保持默认)
            ('/map', '/map'),
            # IMU话题
            ('/imu', '/imu_broadcaster/imu'),
            # {{ AURA-X: Add - Nav2导航action话题映射. }}
            # Nav2导航action (保持默认，与fusion_05_navigation兼容)
            ('/navigate_to_pose', '/navigate_to_pose'),
            ('/follow_waypoints', '/follow_waypoints'),
        ]
    )
    
    # 启动信息
    start_info = LogInfo(
        msg=[
            '\n',
            '========================================\n',
            '  RobotCar Qt GUI 启动中...\n',
            '========================================\n',
            '功能说明:\n',
            '- RViz集成可视化界面\n',
            '- 虚拟摇杆机器人控制\n',
            '- 地图和传感器数据显示\n',
            '- 坐标系管理\n',
            '- Nav2导航控制界面\n',
            '\n',
            '控制说明:\n',
            '- 前进/后退/左转/右转按钮控制机器人\n',
            '- 停止按钮立即停止机器人\n',
            '- 参考坐标系可在界面中修改\n',
            '- 输入坐标后点击Set Goal按钮启动导航\n',
            '- 可随时取消导航或紧急停止\n',
            '\n',
            '话题映射:\n',
            '- 控制: /diff_drive_controller/cmd_vel_unstamped\n',
            '- 里程计: /odometry/filtered (EKF融合)\n',
            '- 激光雷达: /scan\n',
            '- 地图: /map\n',
            '- IMU: /imu\n',
            '- 导航: /navigate_to_pose (Nav2)\n',
            '========================================\n'
        ]
    )
    
    return LaunchDescription([
        # Launch参数
        use_sim_time_arg,
        frame_id_arg,
        robot_base_frame_arg,
        
        # 启动信息
        start_info,
        
        # 节点
        qt_gui_node,
    ])
